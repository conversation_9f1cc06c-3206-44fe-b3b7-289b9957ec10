<?php
session_start();
require '../Includes/dbcon.ini';
require 'query/students.qry';
require 'classes/os_browser.php';

date_default_timezone_set('Asia/Manila');
$current_time = date("h:i:s A");
$current_date = date("Y-m-d");

// Initialize class
$students = new STUDENTS();

// Initialize profile defaults
$profile = [
    'name' => 'Guest',
    'image' => 'assets/default.jpg'
];

// Get device info
$ip_address = getenv("REMOTE_ADDR");
$user_os = getOS();
$user_browser = getBrowser();

// Initialize session variables
if (!isset($_SESSION['time_in'])) $_SESSION['time_in'] = null;
if (!isset($_SESSION['time_out'])) $_SESSION['time_out'] = null;

try {
    // Check if the IP address is registered
    $student_data = $students->check_device_registration($db1, $ip_address);
    
    if ($student_data) {
        $registered = true;
        $profile = $student_data;
        $profile['name'] = $profile['first_name'] . ' ' . $profile['last_name'];
        $profile['image'] = "assets/uploads/" . $profile['image'];
    } else {
        $registered = false;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['time_in']) && $registered) {
            try {
                $db1->beginTransaction();

                // Check for active events and student schedules
                $active_event = $students->get_active_events($db1, $ip_address);

                // Record time in
                $time_in = date('Y-m-d H:i:s');
                $students->record_time_in($db1, $profile['student_no'], $ip_address, $profile['name'], $time_in, $user_os, $user_browser);

                // If there's an active event, create notification
                if ($active_event && $active_event['has_schedule'] > 0) {
                    $notification_message = "Time-in recorded for " . $profile['name'] . 
                                         " at " . date('h:i A') . " for event: " . 
                                         $active_event['activity'];
                    $students->create_notification($db1, $profile['student_no'], $notification_message);
                }

                $db1->commit();
                $_SESSION['time_in'] = $current_time;
                $message = "You have successfully timed in at $current_time on $current_date.";

                echo "<script>
                        const audio = new Audio('assets/timein.mp3');
                        audio.play();
                    </script>";

            } catch(PDOException $e) {
                $db1->rollBack();
                error_log("Error recording time in: " . $e->getMessage());
                $message = "Error: An error occurred while recording time in.";
            }
        }
        
        if (isset($_POST['time_out']) && $registered) {
            try {
                $db1->beginTransaction();

                $time_out = date('Y-m-d H:i:s');
                $_SESSION['time_out'] = $current_time;
                
                $students->record_time_out($db1, $profile['student_no'], $ip_address, $time_out, $user_os, $user_browser, $current_date);

                $db1->commit();
                $message = "You have successfully timed out at $current_time on $current_date.";
                
                echo "<script>
                        const audio = new Audio('assets/timeout.mp3');
                        audio.play();
                    </script>";
            } catch(PDOException $e) {
                $db1->rollBack();
                error_log("Error recording time out: " . $e->getMessage());
                $message = "Error: An error occurred while recording time out.";
            }
        }
    }

    // Get latest attendance status
    if ($registered) {
        $latest_attendance = $students->get_latest_attendance($db1, $profile['student_no'], $current_date);
        $is_active = $latest_attendance && $latest_attendance['time_out'] === null;
    }

} catch(PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $message = "Error: A database error occurred.";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Time In/Time Out</title>
    <link rel="stylesheet" href="css/student.css">
    <style>
        .profile {
            background: url('<?php echo $profile['image']; ?>') no-repeat center center;
            background-size: cover;
        }
        .status-dot {
            background-color: <?php echo isset($is_active) && $is_active ? 'var(--success-color)' : 'var(--error-color)'; ?>;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="profile">
            <div class="status-dot"></div>
        </div>
        <h1>Welcome to Don Bosco, <span style="color:rgb(2, 141, 255);"><?php echo isset($profile['name']) ? $profile['name'] : 'Guest'; ?></span>!</h1>
        <p>Today's Date: <strong><?php echo $current_date; ?></strong></p>

        <?php if (!$registered): ?>
            <p><a href="register.php">Register New Device</a></p>
        <?php else: ?>
            <form method="post">
                <input type="hidden" name="os" value="<?php echo $user_os; ?>">
                <input type="hidden" name="browser" value="<?php echo $user_browser; ?>">
                <button type="submit" name="time_in">Time In</button>
                <button type="submit" name="time_out">Time Out</button>
            </form>
        <?php endif; ?>

        <?php if (isset($message)) : ?>
            <div class="message"> <?php echo $message; ?> </div>
        <?php endif; ?>

        <div class="log">
            <?php
            try {
                $log_data = $registered ? $students->get_latest_log($db1, $profile['student_no'], $current_date) : null;
            } catch(PDOException $e) {
                error_log("Error getting attendance log: " . $e->getMessage());
                $log_data = null;
            }
            ?>
            <p><strong>Time In:</strong> <?php echo $log_data ? $log_data['formatted_time_in'] : 'Not yet timed in'; ?></p>
            <p class="time-out"><strong>Time Out:</strong> <?php echo $log_data && $log_data['formatted_time_out'] ? $log_data['formatted_time_out'] : 'Not yet timed out'; ?></p>
        </div>

        <?php if ($registered): ?>
            <button onclick="window.open('history.php', '_blank')" class="history-btn">
                View Attendance History
            </button>
        <?php endif; ?>
    </div>
</body>
</html>
