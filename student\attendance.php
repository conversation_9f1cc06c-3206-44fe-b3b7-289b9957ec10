<?php
session_start();
require '../core/dbcon.ini';
require 'query/students.qry';
require 'classes/os_browser.php';

date_default_timezone_set('Asia/Manila');
$current_time = date("h:i:s A");
$current_date = date("Y-m-d");

// Initialize class
$students = new STUDENTS();

// Initialize profile defaults
$profile = [
    'name' => 'Guest',
    'image' => 'assets/default.jpg'
];

// Get device info
$ip_address = getenv("REMOTE_ADDR");
$user_os = getOS();
$user_browser = getBrowser();

// Initialize session variables
if (!isset($_SESSION['time_in'])) $_SESSION['time_in'] = null;
if (!isset($_SESSION['time_out'])) $_SESSION['time_out'] = null;

try {
    // Check if the IP address is registered
    $student_data = $students->check_device_registration($db1, $ip_address);

    if ($student_data) {
        $registered = true;
        $profile = $student_data;
        $profile['name'] = $profile['first_name'] . ' ' . $profile['last_name'];
        $profile['image'] = "assets/uploads/" . $profile['image'];
    } else {
        $registered = false;
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['time_in']) && $registered) {
            try {
                $db1->beginTransaction();

                // Record time in
                $time_in = date('Y-m-d H:i:s');
                $students->record_time_in($db1, $profile['student_no'], $ip_address, $profile['name'], $time_in, $user_os, $user_browser);

                $db1->commit();
                $_SESSION['time_in'] = $current_time;
                $message = "You have successfully timed in at $current_time on $current_date.";
                $message_type = "success";

                echo "<script>
                        const audio = new Audio('assets/timein.mp3');
                        audio.play().catch(e => console.log('Audio play failed:', e));
                    </script>";

            } catch(PDOException $e) {
                $db1->rollBack();
                error_log("Error recording time in: " . $e->getMessage());
                $message = "Error: An error occurred while recording time in.";
                $message_type = "error";
            }
        }

        if (isset($_POST['time_out']) && $registered) {
            try {
                $db1->beginTransaction();

                $time_out = date('Y-m-d H:i:s');
                $_SESSION['time_out'] = $current_time;

                $students->record_time_out($db1, $profile['student_no'], $ip_address, $time_out, $user_os, $user_browser, $current_date);

                $db1->commit();
                $message = "You have successfully timed out at $current_time on $current_date.";
                $message_type = "success";

                echo "<script>
                        const audio = new Audio('assets/timeout.mp3');
                        audio.play().catch(e => console.log('Audio play failed:', e));
                    </script>";
            } catch(PDOException $e) {
                $db1->rollBack();
                error_log("Error recording time out: " . $e->getMessage());
                $message = "Error: An error occurred while recording time out.";
                $message_type = "error";
            }
        }
    }

    // Get latest attendance status
    if ($registered) {
        $latest_attendance = $students->get_latest_attendance($db1, $profile['student_no'], $current_date);
        $is_active = $latest_attendance && $latest_attendance['time_out'] === null;
    }

} catch(PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    $message = "Error: A database error occurred.";
    $message_type = "error";
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Don Bosco College - Time In/Time Out</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/student.css">
    <style>
        .profile {
            background: url('<?php echo $profile['image']; ?>') no-repeat center center;
            background-size: cover;
        }
        .status-dot {
            background-color: <?php echo isset($is_active) && $is_active ? 'var(--success-color)' : 'var(--error-color)'; ?>;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="profile">
            <div class="status-dot"></div>
        </div>

        <h1>Welcome to Don Bosco College</h1>
        <p style="color: var(--primary-color); font-weight: 600; font-size: 18px; margin-bottom: 8px;">
            <?php echo isset($profile['name']) ? $profile['name'] : 'Guest'; ?>
        </p>
        <p>Today's Date: <strong><?php echo date('F d, Y'); ?></strong></p>
        <p style="font-size: 14px; color: var(--text-secondary);">Current Time: <strong><?php echo $current_time; ?></strong></p>

        <?php if (!$registered): ?>
            <div style="margin: 30px 0; padding: 20px; background: rgba(255, 215, 0, 0.15); border-radius: 16px; border: 2px solid var(--accent-color);">
                <p style="color: var(--primary-color); font-weight: 600; margin-bottom: 15px;">Device Not Registered</p>
                <p style="font-size: 14px; margin-bottom: 20px;">Please register your device to use the attendance system.</p>
                <a href="register.php" style="display: inline-block; background: var(--accent-color); color: var(--primary-color); padding: 12px 24px; border-radius: 12px; text-decoration: none; font-weight: 600; transition: var(--transition); border: 2px solid var(--primary-color);">Register New Device</a>
            </div>
        <?php else: ?>
            <form method="post" style="margin: 30px 0;">
                <input type="hidden" name="os" value="<?php echo $user_os; ?>">
                <input type="hidden" name="browser" value="<?php echo $user_browser; ?>">

                <button type="submit" name="time_in" style="margin-bottom: 16px;">
                    🕐 Time In
                </button>

                <button type="submit" name="time_out" style="background: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-hover) 100%); color: var(--primary-color); border: 2px solid var(--primary-color); box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);">
                    🕐 Time Out
                </button>
            </form>
        <?php endif; ?>

        <?php if (isset($message)) : ?>
            <div class="message <?php echo isset($message_type) && $message_type === 'error' ? 'error' : ''; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($registered): ?>
        <div class="log">
            <h3 style="color: var(--primary-color); font-size: 16px; margin-bottom: 16px; font-weight: 600;">Today's Attendance</h3>
            <?php
            try {
                $log_data = $students->get_latest_log($db1, $profile['student_no'], $current_date);
            } catch(PDOException $e) {
                error_log("Error getting attendance log: " . $e->getMessage());
                $log_data = null;
            }
            ?>
            <p>
                <strong>Time In:</strong>
                <span style="color: var(--primary-color); font-weight: 600; background: rgba(0, 74, 173, 0.1); padding: 4px 8px; border-radius: 6px;">
                    <?php echo $log_data ? $log_data['formatted_time_in'] : 'Not yet timed in'; ?>
                </span>
            </p>
            <p class="time-out">
                <strong>Time Out:</strong>
                <span style="color: var(--primary-color); font-weight: 600; background: rgba(255, 215, 0, 0.2); padding: 4px 8px; border-radius: 6px;">
                    <?php echo $log_data && $log_data['formatted_time_out'] ? $log_data['formatted_time_out'] : 'Not yet timed out'; ?>
                </span>
            </p>
        </div>

        <button onclick="window.open('history.php', '_blank')" class="history-btn">
            📊 View Attendance History
        </button>
        <?php endif; ?>
    </div>
</body>
</html>
