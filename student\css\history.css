
        :root {
            --primary-color:rgb(0, 58, 250);
            --primary-hover:rgb(34, 101, 247);
            --success-color: #10B981;
            --error-color: #EF4444;
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --bg-primary: #F9FAFB;
            --card-bg: rgba(255, 255, 255, 0.95);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 16px;
            background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%);
            color: var(--text-primary);
            min-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card-bg);
            backdrop-filter: blur(20px) saturate(180%);
            -webkit-backdrop-filter: blur(20px) saturate(180%);
            border-radius: 24px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
                       0 1px 3px rgba(0, 0, 0, 0.05),
                       inset 0 0 0 1px rgba(255, 255, 255, 0.5);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.5);
            position: relative;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            opacity: 0.1;
            border-radius: 24px 24px 100% 100%;
            z-index: 0;
        }

        .header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            padding: 32px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 2;
        }
        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 20px 0;
            padding: 0 20px;
        }

        th, td {
            padding: 16px;
            text-align: left;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }

        th {
            background: rgba(79, 70, 229, 0.03);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            color: var(--text-secondary);
            font-size: 15px;
            transition: var(--transition);
        }

        tr:hover td {
            background: rgba(79, 70, 229, 0.02);
            color: var(--text-primary);
        }

        tbody tr:last-child td {
            border-bottom: none;
        }
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 24px;
            gap: 8px;
            flex-wrap: wrap;
        }
        .pagination a {
            color: var(--primary-color);
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 500;
            transition: var(--transition);
            background: rgba(79, 70, 229, 0.05);
            min-width: 44px;
            text-align: center;
        }
        .pagination a.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
        }
        .pagination a:hover:not(.active) {
            background: rgba(79, 70, 229, 0.1);
            transform: translateY(-1px);
        }
        .no-data {
            text-align: center;
            padding: 48px 24px;
            color: var(--text-secondary);
            font-size: 16px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 16px;
            margin: 24px;
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        .back-btn {
            display: block;
            margin: 24px auto;
            padding: 14px 28px;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
            color: white;
            text-decoration: none;
            border-radius: 16px;
            transition: var(--transition);
            text-align: center;
            max-width: 200px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 14px;
        }
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(79, 70, 229, 0.3);
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
            body {
                padding: 12px;
            }

            .container {
                margin: 0;
                border-radius: 16px;
            }
            
            .header {
                padding: 24px 16px;
            }
            
            .profile {
                width: 100px;
                height: 100px;
                margin-bottom: 16px;
            }
            
            h1 {
                font-size: 22px;
            }
            
            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
                margin: 16px 0;
                padding: 0 16px;
            }
            
            th, td {
                padding: 12px;
                font-size: 14px;
            }

            .pagination {
                padding: 16px;
                gap: 4px;
            }
            
            .pagination a {
                padding: 8px 16px;
                font-size: 13px;
                min-width: 36px;
            }
            
            .back-btn {
                max-width: 180px;
                padding: 12px 24px;
                font-size: 13px;
                margin: 20px auto;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 8px;
            }

            .container {
                border-radius: 12px;
            }

            .profile {
                width: 90px;
                height: 90px;
            }

            h1 {
                font-size: 20px;
            }

            table {
                font-size: 13px;
                padding: 0 12px;
            }

            th, td {
                padding: 10px;
            }

            .back-btn {
                max-width: 160px;
                padding: 10px 20px;
                font-size: 12px;
            }

            .pagination a {
                padding: 6px 12px;
                font-size: 12px;
                min-width: 32px;
            }
        }