:root {
    /* <PERSON> Official Colors */
    --primary-color: #004AAD; /* Deep Royal Blue */
    --primary-hover: #003a8c;
    --accent-color: #FFC300; /* Gold/Yellow */
    --success-color: #4CAF50; /* Fresh Green */
    --error-color: #FF4B4B; /* Alert Red */
    --text-primary: #1F2937;
    --text-secondary: #6B7280;
    --bg-primary: #F5F5F5; /* Light gray sections */
    --card-bg: #FFFFFF; /* White background */
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --shadow-soft: 0 4px 20px rgba(0, 74, 173, 0.1);
    --shadow-medium: 0 8px 30px rgba(0, 74, 173, 0.15);
}

body {
    font-family: 'Poppins', 'Roboto', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, var(--bg-primary) 0%, #e8f0fe 100%);
    color: var(--text-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.container {
    background: var(--card-bg);
    border-radius: 24px;
    box-shadow: var(--shadow-medium);
    width: 90%;
    max-width: 480px;
    padding: 32px;
    text-align: center;
    transition: var(--transition);
    margin: 20px;
    min-height: 600px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(0, 74, 173, 0.1);
}

/* Don Bosco Header with Gold Accent */
.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 8px;
    background: linear-gradient(90deg, var(--accent-color) 0%, #ffdb4d 100%);
    border-radius: 24px 24px 0 0;
    z-index: 1;
}

.container::after {
    content: '';
    position: absolute;
    top: 8px;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    opacity: 0.05;
    border-radius: 0 0 50% 50%;
}

.profile {
    width: 140px;
    height: 140px;
    margin: 0 auto 24px;
    border-radius: 50%;
    border: 3px solid var(--card-bg);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1),
                inset 0 0 0 1px rgba(255, 255, 255, 0.6);
    position: relative;
    transition: var(--transition);
    transform: translateY(0);
    z-index: 1;
    aspect-ratio: 1/1;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    padding: 4px;
}

.profile:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
}

.profile::after {
    content: '';
    position: absolute;
    inset: -4px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), transparent);
    opacity: 0;
    transition: var(--transition);
    z-index: -1;
}

.profile:hover::after {
    opacity: 0.15;
    transform: scale(1.1);
}

.status-dot {
    position: absolute;
    bottom: 5px;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    z-index: 2;
    border: 3px solid var(--card-bg);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
}

/* Don Bosco Logo */
.logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    z-index: 2;
    position: relative;
}

.logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    filter: drop-shadow(0 4px 8px rgba(0, 74, 173, 0.2));
}

h1 {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 16px;
    color: var(--primary-color);
    letter-spacing: -0.02em;
    z-index: 2;
    position: relative;
}

p {
    font-size: 16px;
    margin: 8px 0;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Don Bosco Styled Buttons */
button {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    border-radius: 16px;
    color: white;
    padding: 18px 32px;
    margin: 12px auto;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow-soft);
    width: 100%;
    max-width: 280px;
    display: block;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-family: inherit;
}

button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
    transform: translateY(-100%);
    transition: var(--transition);
}

button:hover::before {
    transform: translateY(0);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(79, 70, 229, 0.2);
}

button:active {
    transform: translateY(0);
}

button:disabled {
    background: linear-gradient(135deg, #E5E7EB 0%, #D1D5DB 100%);
    color: #9CA3AF;
    cursor: not-allowed;
    box-shadow: none;
}

.log {
    margin-top: 24px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.5);
    padding: 24px;
    border-radius: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.log p {
    margin: 12px 0;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.log p:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.log strong {
    color: var(--text-primary);
    font-weight: 600;
    margin-right: 10px;
}

.time-out {
    color: var(--error-color);
    background: rgba(239, 68, 68, 0.05) !important;
}

.message {
    margin-top: 24px;
    padding: 18px 20px;
    background: var(--success-color);
    color: white;
    border-radius: 16px;
    font-size: 15px;
    font-weight: 500;
    box-shadow: 0 8px 20px rgba(76, 175, 80, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    will-change: transform, opacity;
    transform-origin: top;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.message::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.2), transparent);
    transform: translateY(-100%);
    transition: var(--transition);
}

.message:hover::before {
    transform: translateY(0);
}

@keyframes slideIn {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message.error {
    background: var(--error-color);
    box-shadow: 0 8px 20px rgba(255, 75, 75, 0.2);
}

.history-btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    margin-top: 16px;
    border-radius: 16px;
    padding: 12px 20px;
    font-size: 14px;
    transition: var(--transition);
    width: 100%;
    max-width: 200px;
}

.history-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.history-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(52, 152, 219, 0.2);
}

/* Mobile Responsive Design */
@media (max-width: 600px) {
    .container {
        padding: 24px 16px;
        margin: 16px;
        min-height: auto;
    }

    .container::after {
        height: 80px;
    }

    .logo {
        width: 60px;
        height: 60px;
        margin-bottom: 16px;
    }

    .profile {
        width: 110px;
        height: 110px;
        margin-bottom: 16px;
        padding: 3px;
    }

    .status-dot {
        width: 16px;
        height: 16px;
        border-width: 2px;
    }

    h1 {
        font-size: 22px;
        margin-bottom: 12px;
    }

    p {
        font-size: 14px;
        margin: 6px 0;
    }

    button {
        padding: 16px 24px;
        font-size: 15px;
        max-width: 260px;
        border-radius: 14px;
    }

    .message {
        padding: 16px;
        font-size: 14px;
        margin-top: 20px;
        border-radius: 14px;
    }

    .log {
        padding: 20px;
        margin-top: 20px;
        border-radius: 16px;
    }

    .log p {
        padding: 12px 16px;
        font-size: 13px;
    }

    .history-btn {
        margin-top: 16px;
        padding: 12px 24px;
        font-size: 14px;
    }
}

@media (max-width: 400px) {
    .container {
        padding: 20px 12px;
        margin: 12px;
    }

    .container::before {
        height: 60px;
    }

    .profile {
        width: 90px;
        height: 90px;
        margin-bottom: 14px;
        padding: 2px;
    }
    
    .status-dot {
        width: 14px;
        height: 14px;
        border-width: 2px;
        right: 6px;
        bottom: 6px;
    }

    h1 {
        font-size: 20px;
    }

    button {
        padding: 12px 20px;
        font-size: 14px;
        max-width: 180px;
    }

    .log {
        padding: 16px;
    }

    .history-btn {
        padding: 10px 20px;
        font-size: 14px;
        max-width: 160px;
    }
}

/* Additional Don Bosco Styling */
.welcome-text {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

.student-info {
    background: rgba(0, 74, 173, 0.05);
    border-radius: 12px;
    padding: 16px;
    margin: 20px 0;
    border-left: 4px solid var(--accent-color);
}

.time-display {
    font-family: 'Courier New', monospace;
    background: var(--bg-primary);
    padding: 8px 12px;
    border-radius: 8px;
    display: inline-block;
    margin: 0 4px;
    font-weight: 600;
    color: var(--primary-color);
}

/* Button hover effects with Don Bosco colors */
button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 74, 173, 0.25);
}

button:active {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 74, 173, 0.2);
}

/* Gold accent animations */
@keyframes goldShimmer {
    0% { background-position: -200px 0; }
    100% { background-position: 200px 0; }
}

.container::before {
    background: linear-gradient(90deg, var(--accent-color) 0%, #ffdb4d 50%, var(--accent-color) 100%);
    background-size: 200px 100%;
    animation: goldShimmer 3s infinite;
}
