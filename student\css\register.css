
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
}

.form-container {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    border-radius: 20px;
    padding: 2.5rem;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-title {
    color: #fff;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.section-title {
    color: #00b4d8;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    letter-spacing: 0.5px;
}

.info-text {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
    padding: 0.75rem;
    background: rgba(0, 180, 216, 0.1);
    border-radius: 8px;
    border-left: 3px solid #00b4d8;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.info-text i {
    color: #00b4d8;
}

.form-section {
    background: rgba(255, 255, 255, 0.03);
    padding: 1.5rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.7);
    pointer-events: none;
    transition: all 0.3s ease;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 1rem;
    padding-left: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.form-group select option {
    background: #1a1a2e;
    color: #fff;
    padding: 10px;
}

.form-group select:focus option {
    background: #16213e;
}

.form-group input:focus,
.form-group select:focus,
.form-group input:not(:placeholder-shown),
.form-group select:not(:placeholder-shown) {
    border-color: #00b4d8;
    background: rgba(255, 255, 255, 0.15);
}

.form-group input:focus ~ label,
.form-group input:not(:placeholder-shown) ~ label,
.form-group select:focus ~ label,
.form-group select:not(:placeholder-shown) ~ label {
    top: 0;
    left: 1rem;
    font-size: 0.8rem;
    color: #00b4d8;
    background: #1a1a2e;
    padding: 0 0.5rem;
}

.image-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin: 1rem auto;
    border: 3px solid rgba(255, 255, 255, 0.2);
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-preview:hover {
    transform: scale(1.05);
    border-color: #00b4d8;
}

.image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview::before {
    content: 'Upload Photo';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    text-align: center;
    width: 100%;
}

.image-preview.has-image::before {
    display: none;
}

.file-input {
    display: none;
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #00b4d8, #0077b6);
    border: none;
    border-radius: 10px;
    color: #fff;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 180, 216, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

@media (max-width: 480px) {
    .form-container {
        padding: 1.5rem;
    }
    
    .form-title {
        font-size: 1.5rem;
    }
    
    .form-group input,
    .form-group select {
        padding: 0.8rem;
    }
}
