<?php
class STUDENTS
{
    // ************ DEVICE REGISTRATION ************ //

    function check_device_registration($db, $ip_address)
    {
        // Check if this IP address is registered to a student
        $stmt = $db->prepare("SELECT s.Student_ID, s.Last_Name, s.First_Name, s.Middle_Name, s.Department, s.Course, s.Year
                             FROM cdas_student s
                             INNER JOIN cdas_attendance a ON s.Student_ID = a.Student_ID
                             WHERE a.ip_address = ?
                             ORDER BY a.time_in DESC LIMIT 1");
        $stmt->execute(array($ip_address));
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result) {
            return array(
                'student_no' => $result['Student_ID'],
                'first_name' => $result['First_Name'],
                'last_name' => $result['Last_Name'],
                'middle_name' => $result['Middle_Name'],
                'department' => $result['Department'],
                'course' => $result['Course'],
                'year' => $result['Year'],
                'image' => 'default.jpg' // Default image for now
            );
        }
        return false;
    }

    function check_student_exists($db, $student_id)
    {
        $stmt = $db->prepare("SELECT Student_ID FROM cdas_student WHERE Student_ID = ?");
        $stmt->execute(array($student_id));
        return $stmt->rowCount() > 0;
    }

    function register_device($db, $student_id, $ip_address, $os, $browser)
    {
        // First check if student exists
        if (!$this->check_student_exists($db, $student_id)) {
            return false;
        }

        // Register device by creating an initial attendance record
        $time_in = date('Y-m-d H:i:s');
        $stmt = $db->prepare("INSERT INTO cdas_attendance (Student_ID, ip_address, time_in, os, browser)
                             VALUES (?, ?, ?, ?, ?)");
        return $stmt->execute(array($student_id, $ip_address, $time_in, $os, $browser));
    }

    // ************ ATTENDANCE ************ //

    function record_time_in($db, $student_id, $ip_address, $full_name, $time_in, $os, $browser)
    {
        $stmt = $db->prepare("INSERT INTO cdas_attendance (Student_ID, ip_address, time_in, os, browser)
                             VALUES (?, ?, ?, ?, ?)");
        $stmt->execute(array($student_id, $ip_address, $time_in, $os, $browser));
    }

    function record_time_out($db, $student_id, $ip_address, $time_out, $os, $browser, $current_date)
    {
        $stmt = $db->prepare("UPDATE cdas_attendance SET time_out = ?, os = ?, browser = ?
                             WHERE Student_ID = ? AND ip_address = ? AND DATE(time_in) = ? AND time_out IS NULL");
        $stmt->execute(array($time_out, $os, $browser, $student_id, $ip_address, $current_date));
    }

    function get_latest_attendance($db, $student_id, $current_date)
    {
        $stmt = $db->prepare("SELECT time_out FROM cdas_attendance
                             WHERE Student_ID = ? AND DATE(time_in) = ?
                             ORDER BY time_in DESC LIMIT 1");
        $stmt->execute(array($student_id, $current_date));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    function get_latest_log($db, $student_id, $current_date)
    {
        $stmt = $db->prepare("SELECT time_in, time_out,
                             DATE_FORMAT(time_in, '%h:%i %p') as formatted_time_in,
                             DATE_FORMAT(time_out, '%h:%i %p') as formatted_time_out
                             FROM cdas_attendance
                             WHERE Student_ID = ? AND DATE(time_in) = ?
                             ORDER BY time_in DESC LIMIT 1");
        $stmt->execute(array($student_id, $current_date));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    function get_total_attendance_records($db, $student_id)
    {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM cdas_attendance WHERE Student_ID = ?");
        $stmt->execute(array($student_id));
        return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    }

    function get_attendance_records($db, $student_id, $limit, $offset)
    {
        $data = array();
        $stmt = $db->prepare("SELECT *, DATE_FORMAT(time_in, '%M %d, %Y %h:%i %p') as formatted_time_in,
                             DATE_FORMAT(time_out, '%M %d, %Y %h:%i %p') as formatted_time_out
                             FROM cdas_attendance
                             WHERE Student_ID = ?
                             ORDER BY time_in DESC LIMIT ? OFFSET ?");
        $stmt->bindValue(1, $student_id, PDO::PARAM_STR);
        $stmt->bindValue(2, $limit, PDO::PARAM_INT);
        $stmt->bindValue(3, $offset, PDO::PARAM_INT);
        $stmt->execute();
        for ($i = 1; $i <= $stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

    // ************ EVENTS AND NOTIFICATIONS ************ //

    function get_active_events($db, $ip_address)
    {
        // Placeholder for active events - can be expanded later
        return null;
    }

    function create_notification($db, $student_id, $message)
    {
        // Placeholder for notifications - can be expanded later
        return true;
    }

}
?>
