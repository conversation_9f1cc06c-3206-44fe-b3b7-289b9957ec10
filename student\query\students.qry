<?php
class STUDENTS 
{
    // ************ DEVICE ************ //

    function get_student_by_ip($db, $ip_address) 
    {
        $stmt = $db->prepare("SELECT * FROM students WHERE ip_address=?");
        $stmt->execute(array($ip_address));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    function check_student_number($db, $student_no) 
    {
        $stmt = $db->prepare("SELECT student_no FROM students WHERE student_no=?");
        $stmt->execute(array($student_no));
        return $stmt->rowCount();
    }

    function check_ip_address($db, $ip_address) 
    {
        $stmt = $db->prepare("SELECT student_no FROM students WHERE ip_address=?");
        $stmt->execute(array($ip_address));
        return $stmt->rowCount();
    }

    function register_student($db, $student_no, $last_name, $first_name, $middle_name, $suffix_name, $student_id, $course, $academic_year, $term_id, $status, $image, $ip_address, $os, $browser) 
    {
        $stmt = $db->prepare("INSERT INTO students (student_no, last_name, first_name, middle_name, suffix_name, student_id, course, academic_year, term_id, status, image, ip_address, os, browser) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute(array($student_no, $last_name, $first_name, $middle_name, $suffix_name, $student_id, $course, $academic_year, $term_id, $status, $image, $ip_address, $os, $browser));
    }

    // ************ ATTENDANCE ************ //

    function record_time_in($db, $student_no, $ip_address, $full_name, $time_in, $os, $browser) 
    {
        $stmt = $db->prepare("INSERT INTO attendance (student_no, ip_address, full_name, time_in, os, browser) 
        VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute(array($student_no, $ip_address, $full_name, $time_in, $os, $browser));
    }

    function record_time_out($db, $student_no, $ip_address, $time_out, $os, $browser, $current_date) 
    {
        $stmt = $db->prepare("UPDATE attendance SET time_out=?, os=?, browser=? 
        WHERE student_no=? AND ip_address=? AND DATE(time_in)=? AND time_out IS NULL");
        $stmt->execute(array($time_out, $os, $browser, $student_no, $ip_address, $current_date));
    }

    function get_latest_attendance($db, $student_no, $current_date) 
    {
        $stmt = $db->prepare("SELECT time_out FROM attendance WHERE student_no=? AND DATE(time_in)=? ORDER BY time_in DESC LIMIT 1");
        $stmt->execute(array($student_no, $current_date));
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }


    function get_total_attendance_records($db, $student_no) 
    {
        $stmt = $db->prepare("SELECT COUNT(*) as total FROM attendance WHERE student_no=?");
        $stmt->execute(array($student_no));
        return $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    }

    function get_attendance_records($db, $student_no, $limit, $offset) 
    {
        $data = array();
        $stmt = $db->prepare("SELECT * FROM attendance WHERE student_no=? ORDER BY time_in DESC LIMIT ? OFFSET ?");
        $stmt->bindValue(1, $student_no, PDO::PARAM_STR);
        $stmt->bindValue(2, $limit, PDO::PARAM_INT);
        $stmt->bindValue(3, $offset, PDO::PARAM_INT);
        $stmt->execute();
        for ($i = 1; $i <= $stmt->rowCount(); $i++)
            $data[] = $stmt->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

}
?>
