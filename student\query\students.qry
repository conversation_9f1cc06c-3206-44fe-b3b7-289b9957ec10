<?php
class STUDENTS
{
    // ************ DEVICE REGISTRATION ************ //

    function check_device_registration($db, $ip_address)
    {
        $stmt1 = $db->prepare("SELECT cdas_student.Student_ID, cdas_student.Last_Name, cdas_student.First_Name, cdas_student.Middle_Name, cdas_student.Department, cdas_student.Course, cdas_student.Year
                              FROM cdas_student, cdas_attendance
                              WHERE cdas_student.Student_ID = cdas_attendance.Student_ID
                              AND cdas_attendance.ip_address = ?
                              ORDER BY cdas_attendance.time_in DESC LIMIT 1");
        $stmt1->execute(array($ip_address));
        $row = $stmt1->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            return array(
                'student_no' => $row['Student_ID'],
                'first_name' => $row['First_Name'],
                'last_name' => $row['Last_Name'],
                'middle_name' => $row['Middle_Name'],
                'department' => $row['Department'],
                'course' => $row['Course'],
                'year' => $row['Year'],
                'image' => 'default.jpg'
            );
        }
        return false;
    }

    function check_student_exists($db, $student_id)
    {
        $stmt1 = $db->prepare("SELECT Student_ID FROM cdas_student WHERE Student_ID = ?");
        $stmt1->execute(array($student_id));
        return $stmt1->rowCount() > 0;
    }

    function register_device($db, $student_id, $ip_address, $os, $browser)
    {
        if (!$this->check_student_exists($db, $student_id)) {
            return false;
        }

        $time_in = date('Y-m-d H:i:s');
        $stmt1 = $db->prepare("INSERT INTO cdas_attendance (Student_ID, ip_address, time_in, os, browser)
                              VALUES (?, ?, ?, ?, ?)");
        return $stmt1->execute(array($student_id, $ip_address, $time_in, $os, $browser));
    }

    // ************ ATTENDANCE ************ //

    function record_time_in($db, $student_id, $ip_address, $full_name, $time_in, $os, $browser)
    {
        $stmt1 = $db->prepare("INSERT INTO cdas_attendance (Student_ID, ip_address, time_in, os, browser)
                              VALUES (?, ?, ?, ?, ?)");
        $stmt1->execute(array($student_id, $ip_address, $time_in, $os, $browser));
    }

    function record_time_out($db, $student_id, $ip_address, $time_out, $os, $browser, $current_date)
    {
        $stmt1 = $db->prepare("UPDATE cdas_attendance SET time_out = ?, os = ?, browser = ?
                              WHERE Student_ID = ? AND ip_address = ? AND DATE(time_in) = ? AND time_out IS NULL");
        $stmt1->execute(array($time_out, $os, $browser, $student_id, $ip_address, $current_date));
    }

    function get_latest_attendance($db, $student_id, $current_date)
    {
        $stmt1 = $db->prepare("SELECT time_out FROM cdas_attendance
                              WHERE Student_ID = ? AND DATE(time_in) = ?
                              ORDER BY time_in DESC LIMIT 1");
        $stmt1->execute(array($student_id, $current_date));
        return $stmt1->fetch(PDO::FETCH_ASSOC);
    }

    function get_latest_log($db, $student_id, $current_date)
    {
        $stmt1 = $db->prepare("SELECT time_in, time_out,
                              DATE_FORMAT(time_in, '%h:%i %p') as formatted_time_in,
                              DATE_FORMAT(time_out, '%h:%i %p') as formatted_time_out
                              FROM cdas_attendance
                              WHERE Student_ID = ? AND DATE(time_in) = ?
                              ORDER BY time_in DESC LIMIT 1");
        $stmt1->execute(array($student_id, $current_date));
        return $stmt1->fetch(PDO::FETCH_ASSOC);
    }

    function get_total_attendance_records($db, $student_id)
    {
        $stmt1 = $db->prepare("SELECT COUNT(*) as total FROM cdas_attendance WHERE Student_ID = ?");
        $stmt1->execute(array($student_id));
        $row = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    function get_attendance_records($db, $student_id, $limit, $offset)
    {
        $data = array();
        $stmt1 = $db->prepare("SELECT *, DATE_FORMAT(time_in, '%M %d, %Y %h:%i %p') as formatted_time_in,
                              DATE_FORMAT(time_out, '%M %d, %Y %h:%i %p') as formatted_time_out
                              FROM cdas_attendance
                              WHERE Student_ID = ?
                              ORDER BY time_in DESC LIMIT ? OFFSET ?");
        $stmt1->bindValue(1, $student_id, PDO::PARAM_STR);
        $stmt1->bindValue(2, $limit, PDO::PARAM_INT);
        $stmt1->bindValue(3, $offset, PDO::PARAM_INT);
        $stmt1->execute();
        for($i=1; $i<=$stmt1->rowCount(); $i++)
            $data[] = $stmt1->fetch(PDO::FETCH_ASSOC);
        return $data;
    }

}
?>
