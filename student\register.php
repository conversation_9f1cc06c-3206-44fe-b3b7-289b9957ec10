<?php
session_start();
require '../Includes/dbcon.ini';
require 'query/students.qry';
require 'classes/os_browser.php';

date_default_timezone_set('Asia/Manila');
$current_date = date("Y-m-d");

// Initialize class
$students = new STUDENTS();

// Get device info
$ip_address = getenv("REMOTE_ADDR");
$user_os = getOS();
$user_browser = getBrowser();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db1->beginTransaction();
        
        $student_no = $_POST['student_no'];
        $last_name = $_POST['last_name'];
        $first_name = $_POST['first_name'];
        $middle_name = $_POST['middle_name'];
        $suffix_name = $_POST['suffix_name'] ?? '';
        $student_id = $_POST['student_id'];
        $profile_picture = $_FILES['profile_picture'];
        $os = $_POST['os'];
        $browser = $_POST['browser'];
        $course = $_POST['course'];
        $academic_year = $_POST['academic_year'];

        // Check if file was uploaded without errors
        if ($profile_picture['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('File upload error: ' . $profile_picture['error']);
        }

        // Check file type
        $fileType = mime_content_type($profile_picture['tmp_name']);
        if (!in_array($fileType, ['image/jpeg', 'image/png', 'image/gif'])) {
            throw new Exception('Invalid file type. Please upload a JPEG, PNG, or GIF.');
        }

        // Move file to a designated directory
        $target_dir = "assets/uploads";
        if (!file_exists($target_dir)) {
            mkdir($target_dir, 0777, true);
        }

        $target_file = $target_dir . '/' . 'profile' . $student_no . '.' . pathinfo($profile_picture['name'], PATHINFO_EXTENSION);
        if (!move_uploaded_file($profile_picture['tmp_name'], $target_file)) {
            throw new Exception('Failed to move uploaded file.');
        }

        // Check if student number exists
        if ($students->check_student_number($db1, $student_no)) {
            // Store form data in session
            $_SESSION['form_data'] = [
                'student_no' => $student_no,
                'last_name' => $last_name,
                'first_name' => $first_name,
                'middle_name' => $middle_name,
                'suffix_name' => $suffix_name,
                'student_id' => $student_id,
                'course' => $course,
                'academic_year' => $academic_year
            ];
            
            throw new Exception("Student number already exists. Please use a different student number.");
        }

        // Check if IP already exists
        if ($students->check_ip_address($db1, $ip_address)) {
            throw new Exception("This device is already registered.");
        }

        // Get the current term
        $term_data = $students->get_current_term($db1, $academic_year, $course, $current_date);
        
        if (!$term_data) {
            // Try getting upcoming term
            $term_data = $students->get_upcoming_term($db1, $academic_year, $course, $current_date);
            if (!$term_data) {
                throw new Exception("No active or upcoming terms found for your year level and course. Please contact the administrator.");
            }
        }

        // Insert student record into the database
        $status = 'active';
        $image_filename = 'profile' . $student_no . '.' . pathinfo($profile_picture['name'], PATHINFO_EXTENSION);
        
        $students->register_student(
            $db1,
            $student_no,
            $last_name,
            $first_name,
            $middle_name,
            $suffix_name,
            $student_id,
            $course,
            $academic_year,
            $term_data['term_id'],
            $status,
            $image_filename,
            $ip_address,
            $os,
            $browser
        );

        $db1->commit();

        $message = "You have successfully registered for {$term_data['term_name']}. IP Address: $ip_address";
        $redirect_url = preg_match("/(android|webos|iphone|ipad|ipod|blackberry|windows phone)/i", $_SERVER['HTTP_USER_AGENT']) ? 'http://************' : 'http://localhost';
        echo "<script>
            alert('$message'); 
            window.location.href = '$redirect_url/cdas/student/attendance.php';
        </script>";
        exit();

    } catch(Exception $e) {
        $db1->rollBack();
        echo "<script>
            alert('". $e->getMessage() ."'); 
            window.history.back();
        </script>";
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register New Student</title>
    <link href="../vendor/fontawesome-free/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/register.css">
</head>
<body>
    <div class="form-container">
        <h1 class="form-title">Student Registration</h1>
        <form method="post" enctype="multipart/form-data">
            <!-- Image Preview -->
            <div class="image-preview" onclick="document.getElementById('profile_picture').click()">
                <img src="" alt="" id="preview-image">
            </div>
            <input type="file" name="profile_picture" id="profile_picture" class="file-input" accept="image/*" required onchange="previewImage(event)">
            
            <!-- Personal Information -->
            <div class="form-section">
                <div class="section-title">Personal Information</div>
                <div class="form-group">
                    <input type="text" id="student_no" name="student_no" placeholder=" " required>
                    <label for="student_no">Student Number</label>
                </div>
                
                <div class="form-group">
                    <input type="text" id="last_name" name="last_name" placeholder=" " required>
                    <label for="last_name">Last Name</label>
                </div>
                
                <div class="form-group">
                    <input type="text" id="first_name" name="first_name" placeholder=" " required>
                    <label for="first_name">First Name</label>
                </div>
                
                <div class="form-group">
                    <input type="text" id="middle_name" name="middle_name" placeholder=" ">
                    <label for="middle_name">Middle Name</label>
                </div>
                
                <div class="form-group">
                    <input type="text" id="suffix_name" name="suffix_name" placeholder=" ">
                    <label for="suffix_name">Suffix (Jr, Sr, etc)</label>
                </div>
            </div>
            
            <!-- Academic Information -->
            <div class="form-section">
                <div class="section-title">Academic Information</div>
                <div class="info-text">
                    <i class="fas fa-info-circle"></i> You will be automatically enrolled in the current term for your course and year level. If no current term exists, you'll be assigned to the next upcoming term.
                </div>
                
                <div class="form-group">
                    <select name="course" id="course" required>
                        <option value="">Select Course</option>
                        <option value="BTVTEd">Bachelor Technical-Vocational Teacher Education (BTVTEd)</option>
                        <option value="BSIT">Bachelor of Science in Information Technology (BSIT)</option>
                        <option value="DIT">Diploma in Information Technology</option>
                        <option value="DMT">Diploma in Mechanical Technology</option>
                        <option value="DAT">Diploma in Automotive Technology</option>
                        <option value="DEMT">Diploma in Electro-Mechanical Technology</option>
                    </select>
                    <label for="course">Course</label>
                </div>
                
                <div class="form-group">
                    <select name="academic_year" id="academic_year" required>
                        <option value="">Select Year Level</option>
                        <option value="1">1st Year</option>
                        <option value="2">2nd Year</option>
                        <option value="3">3rd Year</option>
                        <option value="4">4th Year</option>
                    </select>
                    <label for="academic_year">Year Level</label>
                </div>
                
                <div class="form-group">
                    <input type="text" id="student_id" name="student_id" placeholder=" ">
                    <label for="student_id">Student ID (Optional)</label>
                </div>
            </div>
            
            <input type="hidden" name="os" value="<?php echo $user_os; ?>">
            <input type="hidden" name="browser" value="<?php echo $user_browser; ?>">
            
            <button type="submit" class="submit-btn">Register</button>
        </form>
    </div>

    <script>
        function previewImage(event) {
            const preview = document.getElementById('preview-image');
            const file = event.target.files[0];
            const reader = new FileReader();
            
            reader.onload = function() {
                preview.src = reader.result;
                preview.parentElement.classList.add('has-image');
            }
            
            if (file) {
                reader.readAsDataURL(file);
            }
        }

        // Restore form data from session if available
        <?php if (isset($_SESSION['form_data'])): ?>
        window.onload = function() {
            <?php foreach ($_SESSION['form_data'] as $field => $value): ?>
            document.getElementById('<?php echo $field; ?>').value = '<?php echo addslashes($value); ?>';
            <?php endforeach; ?>
            <?php unset($_SESSION['form_data']); ?>
        }
        <?php endif; ?>
    </script>
</body>
</html>
