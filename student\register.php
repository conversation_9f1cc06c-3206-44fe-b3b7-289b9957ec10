<?php
session_start();
require '../core/dbcon.ini';
require 'query/students.qry';
require 'classes/os_browser.php';

date_default_timezone_set('Asia/Manila');
$current_date = date("Y-m-d");

// Initialize class
$students = new STUDENTS();

// Get device info
$ip_address = getenv("REMOTE_ADDR");
$user_os = getOS();
$user_browser = getBrowser();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db1->beginTransaction();

        $student_id = $_POST['student_id'];
        $os = $_POST['os'];
        $browser = $_POST['browser'];

        // Check if student exists in cdas_student table
        if (!$students->check_student_exists($db1, $student_id)) {
            throw new Exception("Student ID not found in the system. Please contact the administrator.");
        }

        // Register device for this student
        if ($students->register_device($db1, $student_id, $ip_address, $os, $browser)) {
            $db1->commit();
            $message = "Device successfully registered for Student ID: $student_id. IP Address: $ip_address";
            echo "<script>
                alert('$message');
                window.location.href = 'attendance.php';
            </script>";
            exit();
        } else {
            throw new Exception("Failed to register device. Please try again.");
        }

    } catch(Exception $e) {
        $db1->rollBack();
        echo "<script>
            alert('". $e->getMessage() ."'); 
            window.history.back();
        </script>";
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <title>Don Bosco College - Device Registration</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/student.css">
    <style>
        .register-container {
            background: var(--card-bg);
            border-radius: 24px;
            box-shadow: var(--shadow-medium);
            width: 90%;
            max-width: 400px;
            padding: 40px 32px;
            text-align: center;
            margin: 20px;
            border: 1px solid rgba(0, 74, 173, 0.1);
        }

        .form-group {
            margin-bottom: 24px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--primary-color);
            font-weight: 600;
            font-size: 14px;
        }

        .form-group input {
            width: 100%;
            padding: 16px;
            border: 2px solid #e1e5e9;
            border-radius: 12px;
            font-size: 16px;
            transition: var(--transition);
            background: var(--card-bg);
            box-sizing: border-box;
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .info-box {
            background: rgba(255, 195, 0, 0.1);
            border: 2px solid var(--accent-color);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }

        .info-box h3 {
            color: var(--primary-color);
            margin: 0 0 12px 0;
            font-size: 16px;
        }

        .info-box p {
            margin: 8px 0;
            font-size: 14px;
            color: var(--text-secondary);
        }
    </style>
</head>
<body>
    <div class="register-container">
        <!-- Don Bosco Logo -->
        <div class="logo">
            <img src="../assets/images/logo/dbc.png" alt="Don Bosco College Logo" onerror="this.style.display='none'">
        </div>

        <h1>Device Registration</h1>
        <p style="color: var(--text-secondary); margin-bottom: 30px;">Register your device to access the attendance system</p>

        <div class="info-box">
            <h3>📱 Device Information</h3>
            <p><strong>IP Address:</strong> <?php echo $ip_address; ?></p>
            <p><strong>Operating System:</strong> <?php echo $user_os; ?></p>
            <p><strong>Browser:</strong> <?php echo $user_browser; ?></p>
        </div>

        <form method="post">
            <div class="form-group">
                <label for="student_id">Student ID</label>
                <input type="text" id="student_id" name="student_id" placeholder="Enter your Student ID" required>
            </div>

            <input type="hidden" name="os" value="<?php echo $user_os; ?>">
            <input type="hidden" name="browser" value="<?php echo $user_browser; ?>">

            <button type="submit" style="width: 100%; max-width: none; margin-top: 20px;">
                🔐 Register Device
            </button>
        </form>

        <p style="font-size: 12px; color: var(--text-secondary); margin-top: 20px;">
            Your Student ID must exist in the system. Contact the administrator if you encounter issues.
        </p>

        <a href="attendance.php" style="display: inline-block; margin-top: 16px; color: var(--primary-color); text-decoration: none; font-weight: 500;">
            ← Back to Attendance
        </a>
    </div>
</body>
</html>
